-- Create group_invitations table
CREATE TABLE IF NOT EXISTS group_invitations (
    invitation_id SERIAL PRIMARY KEY,
    group_id INTEGER NOT NULL,
    inviter_id INTEGER NOT NULL,
    invited_user_id INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined')),
    invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP NULL,
    expires_at TIMESTAMP NOT NULL,
    
    -- Foreign key constraints
    FOREIGN KEY (group_id) REFERENCES groups(group_id) ON DELETE CASCADE,
    FOREIGN KEY (inviter_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (invited_user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate pending invitations
    UNIQUE(group_id, invited_user_id, status) DEFER<PERSON>BLE INITIALLY DEFERRED
);

-- Create indexes for better performance
CREATE INDEX idx_group_invitations_group_id ON group_invitations(group_id);
CREATE INDEX idx_group_invitations_invited_user_id ON group_invitations(invited_user_id);
CREATE INDEX idx_group_invitations_status ON group_invitations(status);
CREATE INDEX idx_group_invitations_expires_at ON group_invitations(expires_at);

-- Create partial index for pending invitations only
CREATE INDEX idx_group_invitations_pending ON group_invitations(group_id, invited_user_id) 
WHERE status = 'pending' AND expires_at > CURRENT_TIMESTAMP;
